---
// EditorJS компонент для админ-панели
export interface Props {
  id?: string;
  placeholder?: string;
  data?: any;
  readonly?: boolean;
  minHeight?: number;
  onChange?: string; // имя функции для обработки изменений
}

const {
  id = 'editorjs',
  placeholder = 'Начните писать...',
  data = null,
  readonly = false,
  minHeight = 300,
  onChange = null
} = Astro.props;

const editorId = `editor-${id}`;
const hiddenInputId = `${id}-data`;
---

<div class="editorjs-wrapper">
  <div id={editorId} class="editorjs-container"></div>
  <input type="hidden" id={hiddenInputId} name={id} />
</div>

<!-- Подключаем EditorJS через CDN с таймаутом -->
<script is:inline src="https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest"></script>
<script is:inline src="https://cdn.jsdelivr.net/npm/@editorjs/header@latest"></script>
<script is:inline src="https://cdn.jsdelivr.net/npm/@editorjs/list@latest"></script>

<script is:inline define:vars={{ editorId, hiddenInputId, placeholder, data, readonly, minHeight, onChange }}>
  // Простая и надежная инициализация EditorJS
  function initSimpleEditor() {
    console.log('🚀 Инициализация EditorJS для', editorId);

    // Локальные переменные из Astro props
    const currentEditorId = editorId;
    const currentHiddenInputId = hiddenInputId;
    const currentPlaceholder = placeholder;
    const currentData = data;
    const currentReadonly = readonly;
    const currentMinHeight = minHeight;
    const currentOnChange = onChange;

    // Проверяем доступность библиотек с таймаутом
    let attempts = 0;
    const maxAttempts = 50; // 5 секунд максимум

    function checkLibraries() {
      attempts++;

      if (window.EditorJS && window.Header && window.List) {
        console.log('✅ Все библиотеки загружены, создаем редактор');
        createEditor();
      } else if (attempts < maxAttempts) {
        console.log(`⏳ Попытка ${attempts}/${maxAttempts} - ожидаем библиотеки...`);
        setTimeout(checkLibraries, 100);
      } else {
        console.error('❌ Таймаут загрузки библиотек EditorJS');
        showError();
      }
    }

    function createEditor() {
      try {
        const editor = new window.EditorJS({
          holder: currentEditorId,
          tools: {
            header: {
              class: window.Header,
              config: {
                placeholder: 'Введите заголовок...',
                levels: [1, 2, 3, 4, 5, 6],
                defaultLevel: 2
              }
            },
            list: {
              class: window.List,
              inlineToolbar: true,
              config: {
                defaultStyle: 'unordered'
              }
            }
          },
          data: currentData || {},
          readOnly: currentReadonly,
          placeholder: currentPlaceholder,
          minHeight: currentMinHeight,
          onChange: async (api, event) => {
            try {
              const outputData = await editor.save();
              const hiddenInput = document.getElementById(currentHiddenInputId);

              if (hiddenInput) {
                hiddenInput.value = JSON.stringify(outputData);
              }

              // Сохраняем в глобальную переменную
              window.editorjsLastData = JSON.stringify(outputData);

              // Вызываем пользовательский обработчик
              if (currentOnChange && typeof window[currentOnChange] === 'function') {
                window[currentOnChange](outputData, api, event);
              }

            } catch (error) {
              console.error('❌ Ошибка при сохранении данных EditorJS:', error);
            }
          },
          onReady: () => {
            console.log(`✅ EditorJS ${currentEditorId} готов к работе`);
            hideError();
          }
        });

        // Сохраняем экземпляр
        window[`${currentEditorId}_instance`] = editor;

      } catch (error) {
        console.error('❌ Ошибка при создании EditorJS:', error);
        showError();
      }
    }

    function showError() {
      const container = document.getElementById(currentEditorId);
      if (container) {
        container.innerHTML = `
          <div style="padding: 20px; border: 2px dashed #ef4444; border-radius: 8px; text-align: center; color: #ef4444;">
            <p><strong>Ошибка загрузки редактора</strong></p>
            <p>Не удалось загрузить EditorJS. Проверьте подключение к интернету.</p>
            <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">
              Перезагрузить страницу
            </button>
          </div>
        `;
      }
    }

    function hideError() {
      // Ошибка скрыта, редактор работает
    }

    // Запускаем проверку
    checkLibraries();
  }

  // Инициализация при загрузке DOM
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSimpleEditor);
  } else {
    initSimpleEditor();
  }
</script>

<style>
  .editorjs-wrapper {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    overflow: hidden;
  }

  .editorjs-container {
    min-height: 300px;
    padding: 1rem;
  }

  /* Стили для EditorJS */
  :global(.codex-editor) {
    font-family: inherit;
  }

  :global(.codex-editor__redactor) {
    padding-bottom: 1rem !important;
  }

  :global(.ce-block__content) {
    max-width: none !important;
  }

  :global(.ce-toolbar__content) {
    max-width: none !important;
  }

  :global(.ce-block) {
    margin: 0.5rem 0;
  }

  :global(.ce-paragraph) {
    line-height: 1.6;
  }

  :global(.ce-header) {
    margin: 1rem 0 0.5rem 0;
  }

  :global(.ce-quote) {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
  }

  :global(.ce-code) {
    background: #f3f4f6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    margin: 1rem 0;
  }

  :global(.ce-delimiter) {
    margin: 2rem 0;
    text-align: center;
  }

  :global(.ce-table) {
    margin: 1rem 0;
  }

  :global(.ce-table table) {
    width: 100%;
    border-collapse: collapse;
  }

  :global(.ce-table td) {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
  }

  /* Стили для инлайн тулбара */
  :global(.ce-inline-toolbar) {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Стили для основного тулбара */
  :global(.ce-toolbar) {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
</style>
