---
// EditorJS компонент для админ-панели
export interface Props {
  id?: string;
  placeholder?: string;
  data?: any;
  readonly?: boolean;
  minHeight?: number;
  onChange?: string; // имя функции для обработки изменений
}

const {
  id = 'editorjs',
  placeholder = 'Начните писать...',
  data = null,
  readonly = false,
  minHeight = 300,
  onChange = null
} = Astro.props;

const editorId = `editor-${id}`;
const hiddenInputId = `${id}-data`;
---

<div class="editorjs-wrapper">
  <div id={editorId} class="editorjs-container"></div>
  <input type="hidden" id={hiddenInputId} name={id} />
</div>

<!-- Подключаем EditorJS локально через Vite -->
<script>
  import EditorJS from '@editorjs/editorjs';
  import Header from '@editorjs/header';
  import List from '@editorjs/list';

  // Получаем переменные из Astro
  const editorId = document.currentScript.dataset.editorId;
  const hiddenInputId = document.currentScript.dataset.hiddenInputId;
  const placeholder = document.currentScript.dataset.placeholder;
  const data = document.currentScript.dataset.data ? JSON.parse(document.currentScript.dataset.data) : null;
  const readonly = document.currentScript.dataset.readonly === 'true';
  const minHeight = parseInt(document.currentScript.dataset.minHeight) || 300;
  const onChange = document.currentScript.dataset.onChange;

  console.log('🚀 Инициализация локального EditorJS для', editorId);

  function createEditor() {
    try {
      console.log('🔧 Создаем экземпляр EditorJS...');

      const editor = new EditorJS({
        holder: editorId,
        tools: {
          header: {
            class: Header,
            config: {
              placeholder: 'Введите заголовок...',
              levels: [1, 2, 3, 4, 5, 6],
              defaultLevel: 2
            }
          },
          list: {
            class: List,
            inlineToolbar: true,
            config: {
              defaultStyle: 'unordered'
            }
          }
        },
        data: data || {},
        readOnly: readonly,
        placeholder: placeholder,
        minHeight: minHeight,
        onChange: async (api, event) => {
          try {
            const outputData = await editor.save();
            const hiddenInput = document.getElementById(hiddenInputId);

            if (hiddenInput) {
              hiddenInput.value = JSON.stringify(outputData);
            }

            // Сохраняем в глобальную переменную
            window.editorjsLastData = JSON.stringify(outputData);

            // Вызываем пользовательский обработчик
            if (onChange && typeof window[onChange] === 'function') {
              window[onChange](outputData, api, event);
            }

          } catch (error) {
            console.error('❌ Ошибка при сохранении данных EditorJS:', error);
          }
        },
        onReady: () => {
          console.log(`✅ EditorJS ${editorId} готов к работе`);
        }
      });

      // Сохраняем экземпляр
      window[`${editorId}_instance`] = editor;

    } catch (error) {
      console.error('❌ Ошибка при создании EditorJS:', error);
      showError();
    }
  }

  function showError() {
    const container = document.getElementById(editorId);
    if (container) {
      container.innerHTML = `
        <div style="padding: 20px; border: 2px dashed #ef4444; border-radius: 8px; text-align: center; color: #ef4444;">
          <p><strong>Ошибка загрузки редактора</strong></p>
          <p>Не удалось создать EditorJS. Проверьте консоль для деталей.</p>
          <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Перезагрузить страницу
          </button>
        </div>
      `;
    }
  }

  // Создаем редактор
  createEditor();
</script>

<script is:inline define:vars={{ editorId, hiddenInputId, placeholder, data, readonly, minHeight, onChange }}>
  // Устанавливаем данные для основного скрипта
  const script = document.currentScript.previousElementSibling;
  if (script) {
    script.dataset.editorId = editorId;
    script.dataset.hiddenInputId = hiddenInputId;
    script.dataset.placeholder = placeholder;
    script.dataset.data = data ? JSON.stringify(data) : '';
    script.dataset.readonly = readonly.toString();
    script.dataset.minHeight = minHeight.toString();
    script.dataset.onChange = onChange || '';
  }
</script>

<style>
  .editorjs-wrapper {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    overflow: hidden;
  }

  .editorjs-container {
    min-height: 300px;
    padding: 1rem;
  }

  /* Стили для EditorJS */
  :global(.codex-editor) {
    font-family: inherit;
  }

  :global(.codex-editor__redactor) {
    padding-bottom: 1rem !important;
  }

  :global(.ce-block__content) {
    max-width: none !important;
  }

  :global(.ce-toolbar__content) {
    max-width: none !important;
  }

  :global(.ce-block) {
    margin: 0.5rem 0;
  }

  :global(.ce-paragraph) {
    line-height: 1.6;
  }

  :global(.ce-header) {
    margin: 1rem 0 0.5rem 0;
  }

  :global(.ce-quote) {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
  }

  :global(.ce-code) {
    background: #f3f4f6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    margin: 1rem 0;
  }

  :global(.ce-delimiter) {
    margin: 2rem 0;
    text-align: center;
  }

  :global(.ce-table) {
    margin: 1rem 0;
  }

  :global(.ce-table table) {
    width: 100%;
    border-collapse: collapse;
  }

  :global(.ce-table td) {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
  }

  /* Стили для инлайн тулбара */
  :global(.ce-inline-toolbar) {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Стили для основного тулбара */
  :global(.ce-toolbar) {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
</style>
