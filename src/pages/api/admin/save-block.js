import { loadPageSettings } from '../../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../../settings/utils/settingsSaver.js';

export async function POST(context) {
  const formData = await context.request.formData();
  const data = Object.fromEntries(formData.entries());

  const pageId = data.id;
  const blockId = data.blockId;
  const lang = data.lang || 'ru';
  const content = data.content || '';

  const settings = await loadPageSettings();
  let pages = settings.pages || [];
  const pageIdx = pages.findIndex(p => p.id === pageId);
  if (pageIdx === -1) {
    return new Response('Page not found', { status: 404 });
  }
  const page = pages[pageIdx];
  let blocks = page.blocks || [];
  let block = blocks.find(b => b.id === blockId);

  if (!block) {
    // Новый блок
    block = {
      id: blockId,
      type: data.type || 'text',
      order: Number(data.order) || blocks.length + 1,
      enabled: data.enabled === 'on',
      content: { ru: '', en: '' }
    };
    blocks.push(block);
  }
  // Обновить контент для языка
  block.content = block.content || { ru: '', en: '' };
  block.content[lang] = content;

  // Обновить массив блоков и страницу
  page.blocks = blocks;
  pages[pageIdx] = page;
  settings.pages = pages;
  await savePageSettings(settings);

  return new Response(null, {
    status: 303,
    headers: { Location: `/admin/settings/pages/edit/${pageId}` }
  });
} 