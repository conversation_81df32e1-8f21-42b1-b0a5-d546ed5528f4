---
import AdminLayout from '@layouts/AdminLayout.astro';
---

<AdminLayout title="Простой тест EditorJS">
  <div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Простой тест EditorJS</h1>
    <p class="text-gray-600 mb-6">Проверка работы EditorJS с прямым подключением CDN.</p>
    
    <div class="space-y-6">
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Простой редактор:</label>
        <div id="editorjs" class="border border-gray-200 rounded-lg bg-white min-h-96 p-4"></div>
      </div>
      
      <div class="flex gap-4">
        <button type="button" onclick="saveData()" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">
          Сохранить данные
        </button>
        <button type="button" onclick="clearEditor()" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">
          Очистить
        </button>
      </div>
    </div>
    
    <div id="output" class="mt-8 p-4 bg-gray-100 rounded-lg hidden">
      <h3 class="text-lg font-semibold mb-2">Данные из редактора:</h3>
      <pre id="output-content" class="text-sm bg-white p-4 rounded border overflow-auto max-h-96"></pre>
    </div>
    
    <div id="debug" class="mt-8 p-4 bg-yellow-100 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">Отладочная информация:</h3>
      <div id="debug-content" class="text-sm"></div>
    </div>
  </div>

  <!-- Подключаем EditorJS напрямую -->
  <script is:inline src="https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest"></script>
  <script is:inline src="https://cdn.jsdelivr.net/npm/@editorjs/header@latest"></script>
  <script is:inline src="https://cdn.jsdelivr.net/npm/@editorjs/list@latest"></script>

  <script is:inline>
    let editor = null;
    
    function updateDebug(message) {
      const debugContent = document.getElementById('debug-content');
      const timestamp = new Date().toLocaleTimeString();
      debugContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
    }
    
    function initEditor() {
      updateDebug('Начинаем инициализацию редактора...');
      
      // Проверяем доступность библиотек
      updateDebug(`EditorJS доступен: ${!!window.EditorJS}`);
      updateDebug(`Header доступен: ${!!window.Header}`);
      updateDebug(`List доступен: ${!!window.List}`);
      
      if (!window.EditorJS) {
        updateDebug('❌ EditorJS не загружен');
        return;
      }
      
      if (!window.Header) {
        updateDebug('❌ Header плагин не загружен');
        return;
      }
      
      if (!window.List) {
        updateDebug('❌ List плагин не загружен');
        return;
      }
      
      try {
        updateDebug('Создаем экземпляр редактора...');
        
        editor = new EditorJS({
          holder: 'editorjs',
          tools: {
            header: {
              class: Header,
              config: {
                placeholder: 'Введите заголовок...',
                levels: [1, 2, 3, 4, 5, 6],
                defaultLevel: 2
              }
            },
            list: {
              class: List,
              inlineToolbar: true,
              config: {
                defaultStyle: 'unordered'
              }
            }
          },
          placeholder: 'Начните писать...',
          onReady: () => {
            updateDebug('✅ Редактор готов к работе!');
          },
          onChange: () => {
            updateDebug('📝 Контент изменен');
          }
        });
        
        updateDebug('✅ Экземпляр редактора создан');
        
      } catch (error) {
        updateDebug(`❌ Ошибка при создании редактора: ${error.message}`);
        console.error('Ошибка EditorJS:', error);
      }
    }
    
    function saveData() {
      if (!editor) {
        updateDebug('❌ Редактор не инициализирован');
        return;
      }
      
      editor.save().then((outputData) => {
        updateDebug('✅ Данные сохранены');
        const outputDiv = document.getElementById('output');
        const outputContent = document.getElementById('output-content');
        outputContent.textContent = JSON.stringify(outputData, null, 2);
        outputDiv.classList.remove('hidden');
      }).catch((error) => {
        updateDebug(`❌ Ошибка при сохранении: ${error.message}`);
      });
    }
    
    function clearEditor() {
      if (editor && editor.clear) {
        editor.clear();
        updateDebug('🧹 Редактор очищен');
      }
      
      const outputDiv = document.getElementById('output');
      outputDiv.classList.add('hidden');
    }
    
    // Ждем загрузки всех скриптов
    function waitForScripts() {
      if (window.EditorJS && window.Header && window.List) {
        updateDebug('📦 Все библиотеки загружены, инициализируем редактор');
        initEditor();
      } else {
        updateDebug('⏳ Ожидаем загрузки библиотек...');
        setTimeout(waitForScripts, 100);
      }
    }
    
    // Запускаем после загрузки DOM
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', waitForScripts);
    } else {
      waitForScripts();
    }
  </script>
</AdminLayout>
