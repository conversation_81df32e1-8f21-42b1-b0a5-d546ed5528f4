---
import AdminLayout from '@layouts/AdminLayout.astro';
import EditorJS from '../../components/editor/EditorJS.astro';
---

<AdminLayout title="Тест EditorJS">
  <div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Тест EditorJS</h1>
    <p class="text-gray-600 mb-6">Проверка работы интегрированного EditorJS редактора.</p>
    
    <form class="space-y-6" onsubmit="return handleFormSubmit()">
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Тестовый редактор:</label>
        <EditorJS id="test-content" placeholder="Начните писать тестовый контент..." minHeight={400} />
      </div>
      
      <div class="flex gap-4">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">
          Показать данные
        </button>
        <button type="button" onclick="clearEditor()" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">
          Очистить
        </button>
      </div>
    </form>
    
    <div id="output" class="mt-8 p-4 bg-gray-100 rounded-lg hidden">
      <h3 class="text-lg font-semibold mb-2">Данные из редактора:</h3>
      <pre id="output-content" class="text-sm bg-white p-4 rounded border overflow-auto max-h-96"></pre>
    </div>
  </div>

  <script is:inline>
    function handleFormSubmit() {
      const contentInput = document.querySelector('input[name="test-content"]');
      const outputDiv = document.getElementById('output');
      const outputContent = document.getElementById('output-content');
      
      if (contentInput && contentInput.value) {
        try {
          const data = JSON.parse(contentInput.value);
          outputContent.textContent = JSON.stringify(data, null, 2);
          outputDiv.classList.remove('hidden');
        } catch (error) {
          outputContent.textContent = 'Ошибка парсинга JSON: ' + error.message;
          outputDiv.classList.remove('hidden');
        }
      } else if (window.editorjsLastData) {
        try {
          const data = JSON.parse(window.editorjsLastData);
          outputContent.textContent = JSON.stringify(data, null, 2);
          outputDiv.classList.remove('hidden');
        } catch (error) {
          outputContent.textContent = 'Ошибка парсинга JSON: ' + error.message;
          outputDiv.classList.remove('hidden');
        }
      } else {
        outputContent.textContent = 'Нет данных для отображения';
        outputDiv.classList.remove('hidden');
      }
      
      return false; // Предотвращаем отправку формы
    }
    
    function clearEditor() {
      const editor = window['editor-test-content_instance'];
      if (editor && editor.clear) {
        editor.clear();
      }
      
      const outputDiv = document.getElementById('output');
      outputDiv.classList.add('hidden');
    }
  </script>
</AdminLayout>
