---
import AdminLayout from '@layouts/AdminLayout.astro';
import EditorJS from '../../../../../../../components/editor/EditorJS.astro';
import { loadPageSettings } from '../../../../../../../settings/utils/settingsLoader.js';

const { id } = Astro.params;
const blockId = new URL(Astro.request.url).searchParams.get('blockId');

// Загружаем данные страницы и блока
let block = null;
let page = null;
if (id && blockId) {
  try {
    const settings = await loadPageSettings();
    page = settings.pages?.find(p => p.id === id);
    if (page) {
      block = page.blocks?.find(b => b.id === blockId);
    }
  } catch (error) {
    console.error('Ошибка загрузки блока:', error);
  }
}

// Значения по умолчанию
block = block || {
  id: blockId,
  type: 'text',
  order: 1,
  enabled: true,
  content: { ru: '', en: '' }
};

// Парсим JSON данные для редактора
let editorData = null;
if (block.content?.ru) {
  try {
    editorData = JSON.parse(block.content.ru);
  } catch (error) {
    console.error('Ошибка парсинга данных редактора:', error);
    editorData = null;
  }
}
---

<AdminLayout title={`Редактировать блок: ${blockId} (страница: ${id})`}>
  <div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Редактировать блок</h1>
    <p class="text-gray-600 mb-6">Измените параметры блока и контент. После сохранения изменения будут применены.</p>
    <form action="/api/admin/save-block" method="POST" class="space-y-6" onsubmit="return handleFormSubmit()">
      <input type="hidden" name="id" value={id} />
      <input type="hidden" name="blockId" value={blockId} />
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Тип блока:</label>
          <select name="type" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="text" selected={block.type === 'text'}>Текстовый</option>
            <option value="hero" selected={block.type === 'hero'}>Герой</option>
            <option value="features" selected={block.type === 'features'}>Особенности</option>
            <option value="gallery" selected={block.type === 'gallery'}>Галерея</option>
          </select>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Порядок:</label>
          <input name="order" type="number" value={block.order || 1} min="1" required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div class="flex items-center">
          <input type="checkbox" name="enabled" checked={block.enabled !== false} class="mr-2 rounded border-gray-300 focus:ring-blue-500" />
          <label class="text-sm text-gray-700">Включён</label>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Язык:</label>
          <select name="lang" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="ru">Русский</option>
            <option value="en">English</option>
          </select>
        </div>
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Контент (Editor.js):</label>
        <EditorJS
          id="content"
          placeholder="Редактируйте контент блока..."
          minHeight={400}
          data={editorData}
        />
        <input type="hidden" name="content" id="content-hidden" />
      </div>
      <div class="flex gap-4 mt-8">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">Сохранить</button>
        <a href={`/admin/settings/pages/edit/${id}`} class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">Отмена</a>
      </div>
    </form>
  </div>
  <script is:inline>
    // Обработчик отправки формы
    function handleFormSubmit() {
      const contentInput = document.querySelector('input[name="content"]');
      if (contentInput && window.editorjsLastData) {
        contentInput.value = window.editorjsLastData;
      }
      return true;
    }
  </script>
</AdminLayout>