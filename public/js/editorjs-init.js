// EditorJS инициализация для админ-панели
class EditorJSManager {
  constructor() {
    this.editors = new Map();
    this.isLoaded = false;
    this.loadPromise = null;
  }

  // Загрузка EditorJS и плагинов
  async loadEditorJS() {
    if (this.isLoaded) return;
    if (this.loadPromise) return this.loadPromise;

    this.loadPromise = new Promise(async (resolve, reject) => {
      try {
        // Загружаем EditorJS и плагины через CDN как fallback
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/header@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/list@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/paragraph@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/quote@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/code@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/delimiter@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/table@latest');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/link@latest');

        this.isLoaded = true;
        resolve();
      } catch (error) {
        reject(error);
      }
    });

    return this.loadPromise;
  }

  // Загрузка скрипта
  loadScript(src) {
    return new Promise((resolve, reject) => {
      // Проверяем, не загружен ли уже скрипт
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Создание экземпляра редактора
  async createEditor(config) {
    await this.loadEditorJS();

    const {
      id,
      placeholder = 'Начните писать...',
      data = null,
      readonly = false,
      minHeight = 300,
      onChange = null
    } = config;

    const editorId = `editor-${id}`;
    const hiddenInputId = `${id}-data`;

    // Проверяем, что элемент существует
    const editorElement = document.getElementById(editorId);
    const hiddenInput = document.getElementById(hiddenInputId);
    
    if (!editorElement) {
      console.error(`EditorJS: Элемент с ID ${editorId} не найден`);
      return null;
    }

    // Конфигурация инструментов
    const tools = {
      header: {
        class: window.Header,
        config: {
          placeholder: 'Введите заголовок...',
          levels: [1, 2, 3, 4, 5, 6],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: window.Paragraph,
        config: {
          placeholder: placeholder
        }
      },
      list: {
        class: window.List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      quote: {
        class: window.Quote,
        inlineToolbar: true,
        config: {
          quotePlaceholder: 'Введите цитату',
          captionPlaceholder: 'Автор цитаты'
        }
      },
      code: {
        class: window.Code,
        config: {
          placeholder: 'Введите код...'
        }
      },
      delimiter: window.Delimiter,
      table: {
        class: window.Table,
        inlineToolbar: true,
        config: {
          rows: 2,
          cols: 3
        }
      },
      linkTool: {
        class: window.LinkTool,
        config: {
          endpoint: '/api/admin/fetch-url'
        }
      }
    };

    // Создание экземпляра редактора
    const editor = new window.EditorJS({
      holder: editorId,
      tools: tools,
      data: data || {},
      readOnly: readonly,
      placeholder: placeholder,
      minHeight: minHeight,
      onChange: async (api, event) => {
        try {
          const outputData = await editor.save();
          
          // Сохраняем данные в скрытое поле
          if (hiddenInput) {
            hiddenInput.value = JSON.stringify(outputData);
          }
          
          // Вызываем пользовательский обработчик, если он задан
          if (onChange && typeof window[onChange] === 'function') {
            window[onChange](outputData, api, event);
          }
          
          // Сохраняем данные в глобальную переменную для доступа из форм
          window[`${editorId}_data`] = outputData;
          window.editorjsLastData = JSON.stringify(outputData);
          
        } catch (error) {
          console.error('EditorJS: Ошибка при сохранении данных:', error);
        }
      },
      onReady: () => {
        console.log(`EditorJS: Редактор ${editorId} готов к работе`);
        
        // Устанавливаем минимальную высоту
        const editorContainer = document.querySelector(`#${editorId} .codex-editor`);
        if (editorContainer) {
          editorContainer.style.minHeight = `${minHeight}px`;
        }
      }
    });

    // Сохраняем экземпляр редактора
    this.editors.set(editorId, editor);
    window[`${editorId}_instance`] = editor;
    
    // Функции для работы с данными
    window[`get${editorId}Data`] = async () => {
      try {
        return await editor.save();
      } catch (error) {
        console.error('EditorJS: Ошибка при получении данных:', error);
        return null;
      }
    };

    window[`set${editorId}Data`] = async (newData) => {
      if (newData) {
        try {
          await editor.render(newData);
        } catch (error) {
          console.error('EditorJS: Ошибка при установке данных:', error);
        }
      }
    };
    
    return editor;
  }

  // Получение экземпляра редактора
  getEditor(id) {
    const editorId = `editor-${id}`;
    return this.editors.get(editorId);
  }

  // Уничтожение редактора
  destroyEditor(id) {
    const editorId = `editor-${id}`;
    const editor = this.editors.get(editorId);
    if (editor && editor.destroy) {
      editor.destroy();
      this.editors.delete(editorId);
    }
  }
}

// Создаем глобальный экземпляр менеджера
window.EditorJSManager = new EditorJSManager();

// Функция для быстрой инициализации редактора
window.initEditorJS = async function(config) {
  return await window.EditorJSManager.createEditor(config);
};

// Функция для инициализации редактора с ожиданием загрузки DOM
window.initEditorJSWhenReady = function(config) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.initEditorJS(config);
    });
  } else {
    window.initEditorJS(config);
  }
};
